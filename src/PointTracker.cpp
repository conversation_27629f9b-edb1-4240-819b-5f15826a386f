#include "PointTracker.hpp"
// #include <eigen3/Eigen/Dense>
// #include <limits>
// #include <cmath>
#include <algorithm>
#include <numeric>


// 注意：为了防止内存占用持续增长，我们为suspended轨迹增加TTL并限制池大小

PointTracker::PointTracker(int max_age, int reid_age, float distance_threshold, int min_hits)
    : max_age(max_age), reid_age(reid_age), distance_threshold(distance_threshold), min_hits(min_hits), frame_count(0) {}

void PointTracker::match(
    const std::vector<Eigen::Vector3f>& detections,
    const std::vector<Eigen::Vector3f>& predictions,
    std::vector<std::pair<int, int>>& matched,
    std::vector<int>& unmatched_dets,
    std::vector<int>& unmatched_trks)
{
    matched.clear(); unmatched_dets.clear(); unmatched_trks.clear();
    int N = predictions.size();
    int M = detections.size();

    if (N == 0) {
        unmatched_dets.resize(M);
        std::iota(unmatched_dets.begin(), unmatched_dets.end(), 0);
        return;
    }

    Eigen::MatrixXf cost(N, M);
    for (int i = 0; i < N; ++i)
        for (int j = 0; j < M; ++j)
            cost(i, j) = (predictions[i] - detections[j]).norm();

    auto matches = hungarianMatch(cost);
    std::vector<bool> det_used(M, false);
    std::vector<bool> trk_used(N, false);

    for (auto& [trk_idx, det_idx] : matches) {
        if (trk_idx >= N || det_idx >= M) continue;
        if (cost(trk_idx, det_idx) < distance_threshold) {
            matched.emplace_back(det_idx, trk_idx);
            det_used[det_idx] = true;
            trk_used[trk_idx] = true;
        }
    }

    for (int j = 0; j < M; ++j)
        if (!det_used[j]) unmatched_dets.push_back(j);
    for (int i = 0; i < N; ++i)
        if (!trk_used[i]) unmatched_trks.push_back(i);
}

void PointTracker::reidMatch(
    const std::vector<Eigen::Vector3f>& new_detections,
    std::vector<std::pair<int, std::shared_ptr<KalmanFilter3D>>>& matched_trackers,
    std::vector<int>& unmatched_dets)
{
    matched_trackers.clear(); unmatched_dets.clear();
    int N = suspended.size();
    int M = new_detections.size();

    if (N == 0 || M == 0) {
        unmatched_dets.resize(M);
        std::iota(unmatched_dets.begin(), unmatched_dets.end(), 0);
        return;
    }

    Eigen::MatrixXf cost(N, M);
    for (int i = 0; i < N; ++i)
        for (int j = 0; j < M; ++j)
            cost(i, j) = (suspended[i].trk->getPrediction() - new_detections[j]).norm();

    auto matches = hungarianMatch(cost);
    std::vector<bool> det_used(M, false);
    std::vector<bool> trk_used(N, false);

    for (auto& [trk_idx, det_idx] : matches) {
        if (trk_idx >= N || det_idx >= M) continue;
        if (cost(trk_idx, det_idx) < distance_threshold * 2) {
            matched_trackers.emplace_back(det_idx, suspended[trk_idx].trk);
            det_used[det_idx] = true;
            trk_used[trk_idx] = true;
        }
    }

    for (int j = 0; j < M; ++j)
        if (!det_used[j]) unmatched_dets.push_back(j);

    std::vector<SuspendedTrack> remaining;
    for (int i = 0; i < N; ++i)
        if (!trk_used[i]) remaining.push_back(suspended[i]);
    suspended = std::move(remaining);
}

std::vector<TrackResult> PointTracker::update(const std::vector<Point>& detections) {
    frame_count++;
    std::vector<TrackResult> results;

    // 对挂起轨迹进行老化与清理，避免无限增长
    if (!suspended.empty()) {
        std::vector<SuspendedTrack> aged;
        aged.reserve(suspended.size());
        for (auto &st : suspended) {
            // 让挂起轨迹也随帧推进，从而增加time_since_update
            st.trk->predict();
            st.ttl++;
            if (st.ttl <= reid_age) {
                aged.push_back(st);
            }
        }
        suspended.swap(aged);
        if (suspended.size() > SUSPENDED_MAX_SIZE) {
            // 丢弃最早的部分
            suspended.erase(suspended.begin(), suspended.begin() + (suspended.size() - SUSPENDED_MAX_SIZE));
        }
    }


    std::vector<Eigen::Vector3f> det_positions, det_velocities;
    for (const auto& det : detections) {
        det_positions.emplace_back(det.position[0], det.position[1], det.position[2]);
        det_velocities.emplace_back(det.velocity[0], det.velocity[1], det.velocity[2]);
    }

    std::vector<Eigen::Vector3f> predictions;
    predictions.reserve(trackers.size());
    for (auto& trk : trackers) {
        trk->predict();
        predictions.push_back(trk->getPrediction());
    }

    std::vector<std::pair<int, int>> matched;
    std::vector<int> unmatched_dets, unmatched_trks;
    match(det_positions, predictions, matched, unmatched_dets, unmatched_trks);

    for (auto& [det_idx, trk_idx] : matched) {
        trackers[trk_idx]->update(det_positions[det_idx], det_velocities[det_idx]);
    }

    std::vector<std::shared_ptr<KalmanFilter3D>> new_trackers;
    new_trackers.reserve(trackers.size());
    for (auto& trk : trackers) {
        if (trk->getTimeSinceUpdate() <= max_age) {
            new_trackers.push_back(trk);
        } else if (trk->getTimeSinceUpdate() <= reid_age) {
            // 挂起并加入TTL
            suspended.push_back(SuspendedTrack{trk, 0});
        }
        // 超过reid_age的直接丢弃
    }
    trackers.swap(new_trackers);

    std::vector<Eigen::Vector3f> unmatched_positions;
    unmatched_positions.reserve(unmatched_dets.size());
    for (int idx : unmatched_dets)
        unmatched_positions.push_back(det_positions[idx]);

    std::vector<std::pair<int, std::shared_ptr<KalmanFilter3D>>> reid_matched;
    std::vector<int> still_unmatched;
    reidMatch(unmatched_positions, reid_matched, still_unmatched);

    for (auto& [rel_idx, trk] : reid_matched) {
        int global_idx = unmatched_dets[rel_idx];
        trk->reset(det_positions[global_idx], det_velocities[global_idx]);
        trackers.push_back(trk);
    }

    for (int i : still_unmatched) {
        int global_idx = unmatched_dets[i];
        auto new_trk = std::make_shared<KalmanFilter3D>(
            det_positions[global_idx], det_velocities[global_idx]);
        trackers.push_back(new_trk);
    }

    for (auto& trk : trackers) {
        if (trk->getTimeSinceUpdate() < 1 && (trk->getHitStreak() >= min_hits || frame_count <= min_hits)) {
            if (!trk->hasId() && trk->getHitStreak() >= min_hits)
                trk->assignId();
            if (trk->hasId())
                results.push_back({trk->getPrediction(), trk->getVelocity(), trk->getId()});
        }
    }
    return results;
}

// 获取所有活跃跟踪器的预测结果（无论是否刚更新）
std::vector<TrackResult> PointTracker::getAllActiveTracks(int max_time_since_update) const {
    std::vector<TrackResult> results;

    for (const auto& trk : trackers) {
        // 检查跟踪器是否满足输出条件：
        // 1. 有ID（已确认的跟踪器）
        // 2. 未更新时间不超过阈值
        // 3. 命中次数满足最小要求或在初始阶段
        if (trk->hasId() &&
            trk->getTimeSinceUpdate() <= max_time_since_update &&
            (trk->getHitStreak() >= min_hits || frame_count <= min_hits)) {

            // 如果time_since_update > 1，说明这是预测结果而非更新结果
            results.push_back({trk->getPrediction(), trk->getVelocity(), trk->getId()});
        }
    }

    return results;
}
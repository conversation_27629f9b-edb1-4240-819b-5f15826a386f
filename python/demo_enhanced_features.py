#!/usr/bin/env python3
"""
增强雷达跟踪功能演示脚本

演示三个主要新功能:
1. 120帧分组处理
2. 动态3D可视化  
3. 轨迹插值和平滑
"""

import numpy as np
import matplotlib.pyplot as plt
from trackerv2 import (
    load_radar_data_from_csv,
    RadarTracker,
    cluster_detections,
    smooth_tracking_results,
    visualize_animated_3d
)

def demo_120_frame_grouping():
    """演示120帧分组处理功能"""
    print("\n" + "="*50)
    print("演示1: 120帧分组处理")
    print("="*50)
    
    # 加载数据 - 分组模式
    print("加载数据 (120帧分组模式)...")
    frames_grouped, frame_info_grouped = load_radar_data_from_csv(
        'radar_simulation_data_1.csv',
        group_frames=True,
        frames_per_group=120
    )
    
    # 加载数据 - 正常模式对比
    print("加载数据 (正常逐帧模式)...")
    frames_normal, frame_info_normal = load_radar_data_from_csv(
        'radar_simulation_data_1.csv',
        group_frames=False
    )
    
    print(f"\n对比结果:")
    print(f"正常模式: {len(frames_normal)} 帧")
    print(f"分组模式: {len(frames_grouped)} 组")
    print(f"压缩比例: {len(frames_normal)/len(frames_grouped):.1f}:1")
    
    # 显示第一组的详细信息
    if len(frames_grouped) > 0:
        first_group = frames_grouped[0]
        first_group_info = frame_info_grouped[0]
        print(f"\n第一组详细信息:")
        print(f"  包含帧范围: {first_group_info['frame_range']}")
        print(f"  帧数: {first_group_info['frames_in_group']}")
        print(f"  检测点数: {first_group_info['num_detections']}")
        print(f"  真实目标数: {first_group_info['num_targets']}")
    
    return frames_grouped, frames_normal

def demo_trajectory_interpolation():
    """演示轨迹插值功能"""
    print("\n" + "="*50)
    print("演示2: 轨迹插值和平滑")
    print("="*50)
    
    # 使用分组数据进行跟踪
    frames, frame_info = load_radar_data_from_csv(
        'radar_simulation_data_1.csv',
        group_frames=True,
        frames_per_group=120
    )
    
    if not frames:
        print("无法加载数据")
        return
    
    # 初始化跟踪器
    tracker = RadarTracker(scan_period=6, beam_width=3)
    tracked_history = []
    
    print(f"处理 {len(frames)} 组数据...")
    
    # 跟踪处理
    for frame_idx, frame in enumerate(frames):
        current_frame_info = frame_info.get(frame_idx, {})
        beam_angle = current_frame_info.get('beam_angle', 0)
        
        clustered = cluster_detections(frame, eps=0.001, min_samples=1)
        tracked = tracker.update(clustered, beam_angle=beam_angle)
        tracked_history.append(tracked)
        
        if len(tracked) > 0:
            print(f"组 {frame_idx}: {len(tracked)} 个跟踪目标")
    
    # 进行插值平滑
    print("\n开始轨迹插值...")
    
    methods = ['linear', 'cubic']
    for method in methods:
        print(f"\n测试 {method} 插值:")
        try:
            smoothed = smooth_tracking_results(
                tracked_history, frame_info,
                interpolation_points=5,
                method=method
            )
            print(f"  原始: {len(tracked_history)} 组")
            print(f"  平滑后: {len(smoothed)} 帧")
            print(f"  扩展倍数: {len(smoothed)/len(tracked_history):.1f}x")
        except Exception as e:
            print(f"  {method} 插值失败: {e}")
    
    return tracked_history

def demo_animated_visualization():
    """演示动态3D可视化功能"""
    print("\n" + "="*50)
    print("演示3: 动态3D可视化")
    print("="*50)
    
    # 加载少量数据用于演示
    frames, frame_info = load_radar_data_from_csv(
        'radar_simulation_data_1.csv',
        group_frames=False
    )
    
    if not frames:
        print("无法加载数据")
        return
    
    # 只取前20帧用于演示
    frames_demo = frames[:20]
    
    # 快速跟踪
    tracker = RadarTracker(scan_period=6, beam_width=3)
    tracked_demo = []
    
    print(f"处理 {len(frames_demo)} 帧用于可视化演示...")
    
    for frame_idx, frame in enumerate(frames_demo):
        clustered = cluster_detections(frame, eps=0.001, min_samples=1)
        tracked = tracker.update(clustered, beam_angle=(frame_idx * 3) % 360)
        tracked_demo.append(tracked)
    
    print("\n准备启动动态3D可视化...")
    print("注意: 这将显示交互式3D动画")
    print("每帧会暂停等待用户输入，按Enter继续，输入'q'退出")
    
    # 询问用户是否继续
    user_input = input("\n是否启动动态可视化? (y/n): ")
    if user_input.lower() == 'y':
        visualize_animated_3d(frames_demo, tracked_demo, max_trace_length=10)
    else:
        print("跳过动态可视化演示")
    
    return frames_demo, tracked_demo

def main():
    """主演示函数"""
    print("增强雷达跟踪功能演示")
    print("="*60)
    
    try:
        # 演示1: 120帧分组
        frames_grouped, frames_normal = demo_120_frame_grouping()
        
        # 演示2: 轨迹插值
        tracked_history = demo_trajectory_interpolation()
        
        # 演示3: 动态可视化
        frames_demo, tracked_demo = demo_animated_visualization()
        
        print("\n" + "="*60)
        print("所有演示完成!")
        print("="*60)
        
        print("\n功能总结:")
        print("1. ✓ 120帧分组处理 - 将雷达一圈数据作为一组输入")
        print("2. ✓ 轨迹插值平滑 - 为稀疏跟踪结果生成平滑轨迹")
        print("3. ✓ 动态3D可视化 - 实时显示跟踪效果和真实轨迹")
        
    except Exception as e:
        print(f"演示过程中发生错误: {e}")
        import traceback
        traceback.print_exc()

if __name__ == '__main__':
    main()

#!/usr/bin/env python3
"""
分析逐帧跟踪结果
比较修改后的逐帧处理与原始数据的效果
"""

import pandas as pd
import numpy as np
import matplotlib.pyplot as plt
from mpl_toolkits.mplot3d import Axes3D

def analyze_tracking_results():
    """分析跟踪结果"""
    print("=== 逐帧跟踪结果分析 ===")
    
    # 读取跟踪结果
    try:
        tracked_data = pd.read_csv('output.csv')
        real_data = pd.read_csv('real.csv')
        print(f"跟踪结果: {len(tracked_data)} 条记录")
        print(f"原始数据: {len(real_data)} 条记录")
    except FileNotFoundError as e:
        print(f"文件读取错误: {e}")
        return
    
    # 分析跟踪目标数量
    unique_tracks = tracked_data['id'].nunique()
    unique_real_targets = real_data[real_data['id'] >= 0]['id'].nunique()  # 排除杂波(id=0)
    
    print(f"\n=== 目标统计 ===")
    print(f"跟踪到的目标数量: {unique_tracks}")
    print(f"真实目标数量: {unique_real_targets}")
    
    # 分析帧覆盖情况
    tracked_frames = sorted(tracked_data['frame'].unique())
    real_frames = sorted(real_data['frame'].unique())
    
    print(f"\n=== 帧覆盖分析 ===")
    print(f"有跟踪结果的帧数: {len(tracked_frames)}")
    print(f"有原始数据的帧数: {len(real_frames)}")
    print(f"跟踪帧范围: {min(tracked_frames)} - {max(tracked_frames)}")
    print(f"原始帧范围: {min(real_frames)} - {max(real_frames)}")
    
    # 分析每个跟踪目标的生命周期
    print(f"\n=== 跟踪目标生命周期 ===")
    for track_id in sorted(tracked_data['id'].unique()):
        track_frames = tracked_data[tracked_data['id'] == track_id]['frame'].values
        duration = len(track_frames)
        frame_span = max(track_frames) - min(track_frames) + 1
        continuity = duration / frame_span if frame_span > 0 else 0
        
        print(f"目标 {track_id}: {duration} 帧, 跨度 {frame_span} 帧, "
              f"连续性 {continuity:.2f}, 帧范围 {min(track_frames)}-{max(track_frames)}")
    
    # 可视化跟踪轨迹
    visualize_tracks(tracked_data, real_data)
    
    return tracked_data, real_data

def visualize_tracks(tracked_data, real_data):
    """可视化跟踪轨迹"""
    fig = plt.figure(figsize=(15, 10))
    
    # 3D轨迹图
    ax1 = fig.add_subplot(221, projection='3d')
    
    # 绘制跟踪轨迹
    colors = plt.cm.tab20(np.linspace(0, 1, tracked_data['id'].nunique()))
    for i, track_id in enumerate(sorted(tracked_data['id'].unique())):
        track_data = tracked_data[tracked_data['id'] == track_id]
        ax1.plot(track_data['x'], track_data['y'], track_data['z'], 
                'o-', color=colors[i], label=f'Track {track_id}', markersize=3)
    
    ax1.set_xlabel('X (m)')
    ax1.set_ylabel('Y (m)')
    ax1.set_zlabel('Z (m)')
    ax1.set_title('tracked (3D)')
    # ax1.legend()
    
    # 2D XY平面图
    ax2 = fig.add_subplot(222)
    for i, track_id in enumerate(sorted(tracked_data['id'].unique())):
        track_data = tracked_data[tracked_data['id'] == track_id]
        ax2.plot(track_data['x'], track_data['y'], 
                'o-', color=colors[i], label=f'Track {track_id}', markersize=3)
    
    ax2.set_xlabel('X (m)')
    ax2.set_ylabel('Y (m)')
    ax2.set_title('tracked (XY plane)')
    ax2.grid(True)
    # ax2.legend()
    
    # 时间-距离图
    ax3 = fig.add_subplot(223)
    for i, track_id in enumerate(sorted(tracked_data['id'].unique())):
        track_data = tracked_data[tracked_data['id'] == track_id]
        distances = np.sqrt(track_data['x']**2 + track_data['y']**2 + track_data['z']**2)
        ax3.plot(track_data['frame'], distances, 
                'o-', color=colors[i], label=f'Track {track_id}', markersize=3)
    
    ax3.set_xlabel('frame')
    ax3.set_ylabel('range (m)')
    ax3.set_title('frame-range')
    ax3.grid(True)
    # ax3.legend()
    
    # 原始数据分布
    ax4 = fig.add_subplot(224)
    real_targets = real_data[real_data['id'] >= 0]  # 排除杂波
    real_clutter = real_data[real_data['id'] < 0]  # 杂波
    
    if len(real_targets) > 0:
        ax4.scatter(real_targets['x'], real_targets['y'], 
                   c='red', s=10, alpha=0.6, label='real targets')
    if len(real_clutter) > 0:
        ax4.scatter(real_clutter['x'], real_clutter['y'], 
                   c='gray', s=5, alpha=0.3, label='clutter')
    
    ax4.set_xlabel('X (m)')
    ax4.set_ylabel('Y (m)')
    ax4.set_title('real data distribution')
    ax4.grid(True)
    ax4.legend()
    
    plt.tight_layout()
    plt.savefig('frame_tracking_analysis.png', dpi=300, bbox_inches='tight')
    plt.show()
    
    print(f"\n可视化结果已保存到: frame_tracking_analysis.png")

if __name__ == "__main__":
    tracked_data, real_data = analyze_tracking_results()
    

#!/usr/bin/env python3
"""
增强雷达跟踪测试脚本

功能:
1. 120帧分组处理
2. 动态3D可视化
3. 轨迹插值和平滑

使用方法:
python test_enhanced_tracking.py [mode]

模式:
- normal: 逐帧跟踪 (默认)
- grouped: 120帧分组跟踪
- interpolated: 分组跟踪 + 插值平滑
- animated: 动态3D可视化
- compare: 比较不同模式
"""

import sys
import os
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from trackerv2 import (
    main_test, 
    test_grouped_tracking_with_interpolation,
    compare_tracking_modes,
    visualize_animated_3d,
    load_radar_data_from_csv,
    RadarTracker,
    cluster_detections
)

def test_normal_tracking():
    """测试正常逐帧跟踪"""
    print("="*60)
    print("正常逐帧跟踪测试")
    print("="*60)
    
    frames, tracked_history, metrics = main_test(
        use_grouped_frames=False,
        csv_file='radar_simulation_data_new.csv'
    )
    return frames, tracked_history, metrics

def test_grouped_tracking():
    """测试120帧分组跟踪"""
    print("="*60)
    print("120帧分组跟踪测试")
    print("="*60)

    frames, tracked_history, metrics = main_test(
        use_grouped_frames=True,
        csv_file='radar_simulation_data_new.csv'
    )
    return frames, tracked_history, metrics

def test_animated_visualization():
    """测试动态3D可视化"""
    print("="*60)
    print("动态3D可视化测试")
    print("="*60)
    
    # 先进行跟踪
    frames, tracked_history, metrics = main_test(
        use_grouped_frames=True,
        csv_file='radar_simulation_data_new.csv'
    )
    
    if frames and tracked_history:
        print("\n开始动态3D可视化...")
        print("注意: 这将显示逐帧动画，按Enter继续每一帧")
        
        # 限制帧数以避免过长的演示
        max_frames = min(50, len(frames))
        frames_subset = frames[:max_frames]
        tracked_subset = tracked_history[:max_frames]
        
        visualize_animated_3d(frames_subset, tracked_subset, max_trace_length=20)
    
    return frames, tracked_history, metrics

def test_interpolated_tracking():
    """测试插值平滑跟踪"""
    print("="*60)
    print("插值平滑跟踪测试")
    print("="*60)
    
    return test_grouped_tracking_with_interpolation()

def show_usage():
    """显示使用说明"""
    print(__doc__)

def main():
    """主函数"""
    if len(sys.argv) < 2:
        mode = 'normal'
    else:
        mode = sys.argv[1].lower()
    
    print(f"运行模式: {mode}")
    print("="*60)
    
    try:
        if mode == 'normal':
            test_normal_tracking()
        elif mode == 'grouped':
            test_grouped_tracking()
        elif mode == 'interpolated':
            test_interpolated_tracking()
        elif mode == 'animated':
            test_animated_visualization()
        elif mode == 'compare':
            compare_tracking_modes()
        elif mode == 'help' or mode == '-h' or mode == '--help':
            show_usage()
        else:
            print(f"未知模式: {mode}")
            show_usage()
            return
            
    except KeyboardInterrupt:
        print("\n用户中断测试")
    except Exception as e:
        print(f"测试过程中发生错误: {e}")
        import traceback
        traceback.print_exc()
    
    print("\n测试完成!")

if __name__ == '__main__':
    main()

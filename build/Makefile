# CMAKE generated file: DO NOT EDIT!
# Generated by "Unix Makefiles" Generator, CMake Version 3.25

# Default target executed when no arguments are given to make.
default_target: all
.PHONY : default_target

# Allow only one "make -f Makefile2" at a time, but pass parallelism.
.NOTPARALLEL:

#=============================================================================
# Special targets provided by cmake.

# Disable implicit rules so canonical targets will work.
.SUFFIXES:

# Disable VCS-based implicit rules.
% : %,v

# Disable VCS-based implicit rules.
% : RCS/%

# Disable VCS-based implicit rules.
% : RCS/%,v

# Disable VCS-based implicit rules.
% : SCCS/s.%

# Disable VCS-based implicit rules.
% : s.%

.SUFFIXES: .hpux_make_needs_suffix_list

# Command-line flag to silence nested $(MAKE).
$(VERBOSE)MAKESILENT = -s

#Suppress display of executed commands.
$(VERBOSE).SILENT:

# A target that is always out of date.
cmake_force:
.PHONY : cmake_force

#=============================================================================
# Set environment variables for the build.

# The shell in which to execute make rules.
SHELL = /bin/sh

# The CMake executable.
CMAKE_COMMAND = /home/<USER>/miniconda3/envs/pytorch/lib/python3.8/site-packages/cmake/data/bin/cmake

# The command to remove a file.
RM = /home/<USER>/miniconda3/envs/pytorch/lib/python3.8/site-packages/cmake/data/bin/cmake -E rm -f

# Escaping for special characters.
EQUALS = =

# The top-level source directory on which CMake was run.
CMAKE_SOURCE_DIR = /home/<USER>/My_Project/MSHNet_TensorRT_Infer

# The top-level build directory on which CMake was run.
CMAKE_BINARY_DIR = /home/<USER>/My_Project/MSHNet_TensorRT_Infer/build

#=============================================================================
# Targets provided globally by CMake.

# Special rule for the target edit_cache
edit_cache:
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --cyan "No interactive CMake dialog available..."
	/home/<USER>/miniconda3/envs/pytorch/lib/python3.8/site-packages/cmake/data/bin/cmake -E echo No\ interactive\ CMake\ dialog\ available.
.PHONY : edit_cache

# Special rule for the target edit_cache
edit_cache/fast: edit_cache
.PHONY : edit_cache/fast

# Special rule for the target rebuild_cache
rebuild_cache:
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --cyan "Running CMake to regenerate build system..."
	/home/<USER>/miniconda3/envs/pytorch/lib/python3.8/site-packages/cmake/data/bin/cmake --regenerate-during-build -S$(CMAKE_SOURCE_DIR) -B$(CMAKE_BINARY_DIR)
.PHONY : rebuild_cache

# Special rule for the target rebuild_cache
rebuild_cache/fast: rebuild_cache
.PHONY : rebuild_cache/fast

# The main all target
all: cmake_check_build_system
	$(CMAKE_COMMAND) -E cmake_progress_start /home/<USER>/My_Project/MSHNet_TensorRT_Infer/build/CMakeFiles /home/<USER>/My_Project/MSHNet_TensorRT_Infer/build//CMakeFiles/progress.marks
	$(MAKE) $(MAKESILENT) -f CMakeFiles/Makefile2 all
	$(CMAKE_COMMAND) -E cmake_progress_start /home/<USER>/My_Project/MSHNet_TensorRT_Infer/build/CMakeFiles 0
.PHONY : all

# The main clean target
clean:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/Makefile2 clean
.PHONY : clean

# The main clean target
clean/fast: clean
.PHONY : clean/fast

# Prepare targets for installation.
preinstall: all
	$(MAKE) $(MAKESILENT) -f CMakeFiles/Makefile2 preinstall
.PHONY : preinstall

# Prepare targets for installation.
preinstall/fast:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/Makefile2 preinstall
.PHONY : preinstall/fast

# clear depends
depend:
	$(CMAKE_COMMAND) -S$(CMAKE_SOURCE_DIR) -B$(CMAKE_BINARY_DIR) --check-build-system CMakeFiles/Makefile.cmake 1
.PHONY : depend

#=============================================================================
# Target rules for targets named MSHNet_TensorRT_Infer

# Build rule for target.
MSHNet_TensorRT_Infer: cmake_check_build_system
	$(MAKE) $(MAKESILENT) -f CMakeFiles/Makefile2 MSHNet_TensorRT_Infer
.PHONY : MSHNet_TensorRT_Infer

# fast build rule for target.
MSHNet_TensorRT_Infer/fast:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/MSHNet_TensorRT_Infer.dir/build.make CMakeFiles/MSHNet_TensorRT_Infer.dir/build
.PHONY : MSHNet_TensorRT_Infer/fast

src/HungarianMatcher.o: src/HungarianMatcher.cpp.o
.PHONY : src/HungarianMatcher.o

# target to build an object file
src/HungarianMatcher.cpp.o:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/MSHNet_TensorRT_Infer.dir/build.make CMakeFiles/MSHNet_TensorRT_Infer.dir/src/HungarianMatcher.cpp.o
.PHONY : src/HungarianMatcher.cpp.o

src/HungarianMatcher.i: src/HungarianMatcher.cpp.i
.PHONY : src/HungarianMatcher.i

# target to preprocess a source file
src/HungarianMatcher.cpp.i:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/MSHNet_TensorRT_Infer.dir/build.make CMakeFiles/MSHNet_TensorRT_Infer.dir/src/HungarianMatcher.cpp.i
.PHONY : src/HungarianMatcher.cpp.i

src/HungarianMatcher.s: src/HungarianMatcher.cpp.s
.PHONY : src/HungarianMatcher.s

# target to generate assembly for a file
src/HungarianMatcher.cpp.s:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/MSHNet_TensorRT_Infer.dir/build.make CMakeFiles/MSHNet_TensorRT_Infer.dir/src/HungarianMatcher.cpp.s
.PHONY : src/HungarianMatcher.cpp.s

src/KalmFilter3D.o: src/KalmFilter3D.cpp.o
.PHONY : src/KalmFilter3D.o

# target to build an object file
src/KalmFilter3D.cpp.o:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/MSHNet_TensorRT_Infer.dir/build.make CMakeFiles/MSHNet_TensorRT_Infer.dir/src/KalmFilter3D.cpp.o
.PHONY : src/KalmFilter3D.cpp.o

src/KalmFilter3D.i: src/KalmFilter3D.cpp.i
.PHONY : src/KalmFilter3D.i

# target to preprocess a source file
src/KalmFilter3D.cpp.i:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/MSHNet_TensorRT_Infer.dir/build.make CMakeFiles/MSHNet_TensorRT_Infer.dir/src/KalmFilter3D.cpp.i
.PHONY : src/KalmFilter3D.cpp.i

src/KalmFilter3D.s: src/KalmFilter3D.cpp.s
.PHONY : src/KalmFilter3D.s

# target to generate assembly for a file
src/KalmFilter3D.cpp.s:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/MSHNet_TensorRT_Infer.dir/build.make CMakeFiles/MSHNet_TensorRT_Infer.dir/src/KalmFilter3D.cpp.s
.PHONY : src/KalmFilter3D.cpp.s

src/PointTracker.o: src/PointTracker.cpp.o
.PHONY : src/PointTracker.o

# target to build an object file
src/PointTracker.cpp.o:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/MSHNet_TensorRT_Infer.dir/build.make CMakeFiles/MSHNet_TensorRT_Infer.dir/src/PointTracker.cpp.o
.PHONY : src/PointTracker.cpp.o

src/PointTracker.i: src/PointTracker.cpp.i
.PHONY : src/PointTracker.i

# target to preprocess a source file
src/PointTracker.cpp.i:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/MSHNet_TensorRT_Infer.dir/build.make CMakeFiles/MSHNet_TensorRT_Infer.dir/src/PointTracker.cpp.i
.PHONY : src/PointTracker.cpp.i

src/PointTracker.s: src/PointTracker.cpp.s
.PHONY : src/PointTracker.s

# target to generate assembly for a file
src/PointTracker.cpp.s:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/MSHNet_TensorRT_Infer.dir/build.make CMakeFiles/MSHNet_TensorRT_Infer.dir/src/PointTracker.cpp.s
.PHONY : src/PointTracker.cpp.s

src/Utils.o: src/Utils.cpp.o
.PHONY : src/Utils.o

# target to build an object file
src/Utils.cpp.o:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/MSHNet_TensorRT_Infer.dir/build.make CMakeFiles/MSHNet_TensorRT_Infer.dir/src/Utils.cpp.o
.PHONY : src/Utils.cpp.o

src/Utils.i: src/Utils.cpp.i
.PHONY : src/Utils.i

# target to preprocess a source file
src/Utils.cpp.i:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/MSHNet_TensorRT_Infer.dir/build.make CMakeFiles/MSHNet_TensorRT_Infer.dir/src/Utils.cpp.i
.PHONY : src/Utils.cpp.i

src/Utils.s: src/Utils.cpp.s
.PHONY : src/Utils.s

# target to generate assembly for a file
src/Utils.cpp.s:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/MSHNet_TensorRT_Infer.dir/build.make CMakeFiles/MSHNet_TensorRT_Infer.dir/src/Utils.cpp.s
.PHONY : src/Utils.cpp.s

src/main.o: src/main.cpp.o
.PHONY : src/main.o

# target to build an object file
src/main.cpp.o:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/MSHNet_TensorRT_Infer.dir/build.make CMakeFiles/MSHNet_TensorRT_Infer.dir/src/main.cpp.o
.PHONY : src/main.cpp.o

src/main.i: src/main.cpp.i
.PHONY : src/main.i

# target to preprocess a source file
src/main.cpp.i:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/MSHNet_TensorRT_Infer.dir/build.make CMakeFiles/MSHNet_TensorRT_Infer.dir/src/main.cpp.i
.PHONY : src/main.cpp.i

src/main.s: src/main.cpp.s
.PHONY : src/main.s

# target to generate assembly for a file
src/main.cpp.s:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/MSHNet_TensorRT_Infer.dir/build.make CMakeFiles/MSHNet_TensorRT_Infer.dir/src/main.cpp.s
.PHONY : src/main.cpp.s

# Help Target
help:
	@echo "The following are some of the valid targets for this Makefile:"
	@echo "... all (the default if no target is provided)"
	@echo "... clean"
	@echo "... depend"
	@echo "... edit_cache"
	@echo "... rebuild_cache"
	@echo "... MSHNet_TensorRT_Infer"
	@echo "... src/HungarianMatcher.o"
	@echo "... src/HungarianMatcher.i"
	@echo "... src/HungarianMatcher.s"
	@echo "... src/KalmFilter3D.o"
	@echo "... src/KalmFilter3D.i"
	@echo "... src/KalmFilter3D.s"
	@echo "... src/PointTracker.o"
	@echo "... src/PointTracker.i"
	@echo "... src/PointTracker.s"
	@echo "... src/Utils.o"
	@echo "... src/Utils.i"
	@echo "... src/Utils.s"
	@echo "... src/main.o"
	@echo "... src/main.i"
	@echo "... src/main.s"
.PHONY : help



#=============================================================================
# Special targets to cleanup operation of make.

# Special rule to run CMake to check the build system integrity.
# No rule that depends on this can have commands that come from listfiles
# because they might be regenerated.
cmake_check_build_system:
	$(CMAKE_COMMAND) -S$(CMAKE_SOURCE_DIR) -B$(CMAKE_BINARY_DIR) --check-build-system CMakeFiles/Makefile.cmake 0
.PHONY : cmake_check_build_system


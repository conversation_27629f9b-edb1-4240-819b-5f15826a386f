#include <iostream>
#include <vector>
#include <fstream>
#include <sstream>
#include <map>
#include <algorithm>
#include "PointTracker.hpp"
#include "Utils.hpp"

// CSV数据读取函数 - 按frame列从1开始连续读取
std::vector<std::vector<Point>> loadRadarDataFromCSV(const std::string& filename) {
    std::ifstream file(filename);
    if (!file.is_open()) {
        std::cerr << "无法打开CSV文件: " << filename << std::endl;
        return {};
    }

    std::string line;
    std::getline(file, line); // 跳过标题行

    std::map<int, std::vector<Point>> frame_data;
    int max_frame = 0;
    int min_frame = INT_MAX;

    // 第一遍读取：收集所有数据并找到帧范围
    while (std::getline(file, line)) {
        if (line.empty()) continue;

        std::stringstream ss(line);
        std::string cell;
        std::vector<std::string> row;

        // 解析CSV行
        while (std::getline(ss, cell, ',')) {
            // 去除前后空格
            cell.erase(0, cell.find_first_not_of(" \t"));
            cell.erase(cell.find_last_not_of(" \t") + 1);
            row.push_back(cell);
        }

        if (row.size() < 12) continue; // 确保有足够的列

        try {
            Point point;
            point.velocity[0] = std::stof(row[0]); // vx
            point.velocity[1] = std::stof(row[1]); // vy
            point.velocity[2] = std::stof(row[2]); // vz
            point.position[0] = std::stof(row[3]); // x
            point.position[1] = std::stof(row[4]); // y
            point.position[2] = std::stof(row[5]); // z
            // fMV, fMR, fMA, fME 暂时不使用
            point.type = std::stoi(row[10]); // type
            point.frame = std::stoi(row[11]); // frame
            point.label = -1; // 初始化为杂波标签

            frame_data[point.frame].push_back(point);
            max_frame = std::max(max_frame, point.frame);
            min_frame = std::min(min_frame, point.frame);
        } catch (const std::exception& e) {
            std::cerr << "解析CSV行时出错: " << line << std::endl;
            continue;
        }
    }

    file.close();

    if (frame_data.empty()) {
        std::cerr << "CSV文件中没有有效数据" << std::endl;
        return {};
    }

    // 从帧1开始创建连续的帧数据，缺失的帧用空向量填充
    int start_frame = 1;
    int total_frames = max_frame - start_frame + 1;
    std::vector<std::vector<Point>> all_detections(total_frames);

    for (int frame = start_frame; frame <= max_frame; ++frame) {
        int index = frame - start_frame; // 转换为数组索引
        if (frame_data.find(frame) != frame_data.end()) {
            all_detections[index] = frame_data[frame];
            // 更新帧号为连续索引
            for (auto& point : all_detections[index]) {
                point.frame = index;
            }
        }
        // 如果frame不存在，all_detections[index]保持为空向量
    }

    std::cout << "从CSV文件读取完毕，帧范围: " << min_frame << "-" << max_frame
              << "，连续帧数: " << total_frames
              << "，有数据的帧数: " << frame_data.size() << std::endl;

    return all_detections;
}

int main() {
    const std::string csv_file = "python/radar_simulation_data_4.csv";

    // ---------- 1. 从CSV文件读取雷达数据 ----------
    std::vector<std::vector<Point>> all_detections = loadRadarDataFromCSV(csv_file);

    if (all_detections.empty()) {
        std::cerr << "无法读取数据，程序退出" << std::endl;
        return -1;
    }

    // 保存原始数据的副本用于输出
    std::vector<std::vector<Point>> original_detections = all_detections;

    // 统计有数据的帧
    int frames_with_data = 0;
    for (const auto& frame : all_detections) {
        if (!frame.empty()) frames_with_data++;
    }

    std::cout << "数据统计 - 总帧数: " << all_detections.size()
              << ", 有数据帧数: " << frames_with_data << std::endl;

    // ---------- 2. 初始化跟踪器 ----------
    // 调整参数以适应扫描雷达逐帧处理：
    // max_age: 60帧 (约3秒，允许目标在扫描间隙中暂时丢失)
    // reid_age: 240帧 (约12秒，支持长时间重识别)
    // distance_threshold: 100.0f (适应预测误差)
    // min_hits: 2 (降低确认阈值)
    PointTracker tracker(60, 240, 100.0f, 2);
    std::vector<std::vector<TrackResult>> tracked_results;

    // ---------- 3. 逐帧处理 ------------
    int num_frames = all_detections.size();

    std::cout << "开始逐帧处理，总帧数: " << num_frames << std::endl;

    for (int frame = 0; frame < num_frames; ++frame) {
        const auto& detections_raw = all_detections[frame];

        // 准备当前帧的检测点
        std::vector<Point> frame_points;
        for (const auto& point : detections_raw) {
            Point p = point;
            p.frame = frame;  // 保持原始帧号
            p.label = -1;
            frame_points.push_back(p);
        }

        // 对当前帧进行聚类（调整参数适应单帧数据）
        std::vector<Point> clustered;
        if (!frame_points.empty()) {
            // 对于单帧数据，使用更宽松的聚类参数
            clustered = clusterDetections_DBSCAN(frame_points, 15.0f, 1, 4);
        }

        // 更新跟踪器
        std::vector<TrackResult> tracks = tracker.update(clustered);

        // 获取所有活跃跟踪器的结果（包括预测结果）
        // 参数5表示：最多允许5帧未更新的跟踪器输出预测结果
        std::vector<TrackResult> all_active_tracks = tracker.getAllActiveTracks(100);

        // 输出调试信息
        if (all_active_tracks.size() > 0) {
            int updated_tracks = tracks.size();  // 刚更新的跟踪器数量
            int predicted_tracks = all_active_tracks.size() - updated_tracks;  // 预测的跟踪器数量
            std::cout << "Frame " << frame << ": " << updated_tracks << " updated, "
                      << predicted_tracks << " predicted, total " << all_active_tracks.size() << " tracks" << std::endl;
        }

        // 保存所有活跃跟踪器的结果（包括预测结果）
        tracked_results.push_back(all_active_tracks);

        // 每100帧输出一次进度
        if (frame % 100 == 0 || frame == num_frames - 1) {
            std::cout << "处理进度: " << frame + 1 << "/" << num_frames
                      << " (" << (100.0 * (frame + 1) / num_frames) << "%)"
                      << ", 当前跟踪目标数: " << tracks.size() << std::endl;
        }
    }
    // ---------- 4. 输出结果 ----------
    writeResultsToCSV(tracked_results, "output.csv");
    writePointsToCSV(original_detections, "real.csv");

    std::cout << "跟踪完成，结果已保存到 output.csv 和 real.csv" << std::endl;
    return 0;    
}




import numpy as np
import matplotlib.pyplot as plt
from mpl_toolkits.mplot3d import Axes3D
from sklearn.cluster import DBSCAN
from sklearn.preprocessing import StandardScaler
from filterpy.kalman import KalmanFilter
from scipy.optimize import linear_sum_assignment
from scipy.interpolate import interp1d
from collections import defaultdict
import warnings
import pandas as pd
warnings.filterwarnings("ignore", category=np.VisibleDeprecationWarning)

# ---------- 数据读取与聚类 ----------
def load_radar_data_from_csv(csv_file='radar_simulation_data.csv', group_frames=False, frames_per_group=120):
    """
    从CSV文件读取雷达点迹数据，按frame列的值分组

    参数:
    - csv_file: CSV文件路径
    - group_frames: 是否按组处理帧数据（默认False）
    - frames_per_group: 每组包含的帧数范围（默认120）

    返回:
    - frames: 按帧/组组织的数据列表
    - frame_info: 帧/组信息字典
    """
    try:
        df = pd.read_csv(csv_file)
        print(f"成功读取CSV文件: {csv_file}")
        print(f"数据总数: {len(df)} 个点")

        # 检查必要的列 - 新格式
        required_cols = ['vx', 'vy', 'vz', 'x', 'y', 'z', 'fMV', 'fMR', 'fMA', 'fME', 'type', 'frame']
        missing_cols = [col for col in required_cols if col not in df.columns]
        if missing_cols:
            raise ValueError(f"CSV文件缺少必要的列: {missing_cols}")

        # 统计信息 - 基于type字段
        true_targets = df[df['type'] > 0]  # type=1为真实目标
        clutter_points = df[df['type'] == -1]  # type=0为杂波
        print(f"真实目标点数: {len(true_targets)}")
        print(f"杂波点数: {len(clutter_points)}")

        # 由于新格式没有target_id，我们通过聚类来估计目标数量
        if len(true_targets) > 0:
            # 简单的基于位置的聚类来估计目标数量
            from sklearn.cluster import DBSCAN
            positions = true_targets[['x', 'y', 'z']].values
            clustering = DBSCAN(eps=100, min_samples=3).fit(positions)  # 100米聚类半径
            unique_targets = len(set(clustering.labels_)) - (1 if -1 in clustering.labels_ else 0)
            print(f"估计目标数量: {unique_targets} (基于位置聚类)")
        else:
            unique_targets = 0
            print("未检测到目标")

        # 按帧组织数据
        frames = []
        frame_info = {}

        # 获取所有帧号并排序
        all_frame_numbers = sorted(df['frame'].unique())
        min_frame = all_frame_numbers[0]
        max_frame = all_frame_numbers[-1]
        print(f"帧范围: {min_frame} - {max_frame}")

        if group_frames:
            print(f"启用分组处理模式：每{frames_per_group}帧值范围为一组")
            
            # 计算总组数
            num_groups = ((max_frame - min_frame) // frames_per_group) + 1
            print(f"将分为 {num_groups} 组")

            for group_idx in range(num_groups):
                # 计算当前组的frame值范围
                start_frame_val = min_frame + group_idx * frames_per_group
                end_frame_val = start_frame_val + frames_per_group - 1
                
                # 获取属于当前组的所有frame编号
                group_frames = [f for f in all_frame_numbers 
                              if start_frame_val <= f <= end_frame_val]
                
                if not group_frames:
                    continue  # 跳过空组

                print(f"处理第 {group_idx + 1} 组: frame值 {start_frame_val}-{end_frame_val} "
                      f"(实际包含 {len(group_frames)} 个不同帧)")

                # 获取该组所有数据点
                group_mask = df['frame'].isin(group_frames)
                group_data = df[group_mask]
                
                # 构建点数据数组 [x, y, z, vx, vy, vz, fMV, fMR, fMA, fME, type]
                # 为了保持兼容性，我们需要计算一个时间值
                avg_frame = np.mean(group_frames)
                estimated_time = avg_frame * 0.05  # 假设每帧0.05秒间隔

                # 构建包含所有新字段的数据数组
                group_points = []
                for _, row in group_data.iterrows():
                    point = [
                        row['x'], row['y'], row['z'],     # 位置
                        estimated_time,                   # 时间（估计值）
                        row['type'],                      # 目标类型（替代target_id）
                        row['vx'], row['vy'], row['vz'],  # 速度分量
                        row['fMV'], row['fMR'],           # 径向速度和距离
                        row['fMA'], row['fME']            # 方位角和俯仰角
                    ]
                    group_points.append(point)

                frames.append(np.array(group_points))

                # 收集组信息
                frame_info[group_idx] = {
                    'time': estimated_time,
                    'beam_angle': group_data['fMA'].mean(),  # 使用方位角平均值
                    'num_detections': len(group_data),
                    'num_targets': len(group_data[group_data['type'] > 0 ]),
                    'num_clutter': len(group_data[group_data['type'] < 0]),
                    'frame_range': (start_frame_val, end_frame_val),
                    'frames_in_group': len(group_frames)  # 实际包含的帧数
                }

        else:
            # 原始逐帧处理模式
            for frame_num in all_frame_numbers:
                frame_data = df[df['frame'] == frame_num]

                # 构建包含所有新字段的数据数组
                frame_points = []
                estimated_time = frame_num * 0.05  # 假设每帧0.05秒间隔

                for _, row in frame_data.iterrows():
                    point = [
                        row['x'], row['y'], row['z'],     # 位置
                        estimated_time,                   # 时间（估计值）
                        row['type'],                      # 目标类型（替代target_id）
                        row['vx'], row['vy'], row['vz'],  # 速度分量
                        row['fMV'], row['fMR'],           # 径向速度和距离
                        row['fMA'], row['fME']            # 方位角和俯仰角
                    ]
                    frame_points.append(point)

                frames.append(np.array(frame_points))

                # 保存帧信息
                frame_info[frame_num] = {
                    'time': estimated_time,
                    'beam_angle': frame_data['fMA'].mean(),  # 使用方位角平均值
                    'num_detections': len(frame_data),
                    'num_targets': len(frame_data[frame_data['type'] > 0]),
                    'num_clutter': len(frame_data[frame_data['type'] < 0])
                }

        print(f"成功加载 {len(frames)} {'组' if group_frames else '帧'}数据")

        # 打印目标统计信息（基于聚类结果）
        if len(true_targets) > 0:
            print("\n=== 目标统计信息 ===")
            # 由于新格式没有target_id，我们基于时间和位置来分析目标轨迹
            frame_groups = true_targets.groupby('frame')
            total_detections_by_frame = {}

            for frame_num, frame_group in frame_groups:
                total_detections_by_frame[frame_num] = len(frame_group)

            print(f"总目标检测点: {len(true_targets)}")
            print(f"分布在 {len(total_detections_by_frame)} 个帧中")
            print(f"平均每帧检测: {len(true_targets)/len(total_detections_by_frame):.1f} 个目标")

            # 速度和距离统计
            print(f"径向速度范围: {true_targets['fMV'].min():.2f} - {true_targets['fMV'].max():.2f} m/s")
            print(f"距离范围: {true_targets['fMR'].min():.1f} - {true_targets['fMR'].max():.1f} m")

        return frames, frame_info

    except Exception as e:
        print(f"读取CSV文件失败: {e}")
        print("将使用原始的模拟数据生成函数")
        return generate_synthetic_radar_data_backup()

def generate_synthetic_radar_data_backup(num_frames=120, num_targets=5, scan_period=6.0, beam_width=3.0,
                                 fps=20, noise_std=3.0, detection_range=1000):
    """
    生成360度扫描雷达模拟数据（备份函数）

    参数:
    - num_frames: 总帧数 (默认120帧 = 6秒 * 20fps)
    - num_targets: 目标数量
    - scan_period: 扫描周期(秒) - 6秒旋转一圈
    - beam_width: 波束宽度(度) - 每帧3度数据
    - fps: 帧率 - 一秒20帧
    - noise_std: 噪声标准差
    - detection_range: 雷达探测距离(米)
    """
    data = []
    np.random.seed(42)

    # 初始化目标状态（确保在雷达检测范围内）
    base_positions = np.random.uniform(-300, 300, size=(num_targets, 3))  # 减小初始范围
    base_velocities = np.random.uniform(-8, 8, size=(num_targets, 3))  # 减小速度避免快速离开

    # 计算时间参数
    frame_time_interval = 1.0 / fps  # 每帧时间间隔 = 0.05秒
    degrees_per_frame = 360.0 / (scan_period * fps)  # 每帧扫描角度 = 3度

    print(f"雷达参数:")
    print(f"  扫描周期: {scan_period}秒")
    print(f"  帧率: {fps}fps")
    print(f"  每帧时间间隔: {frame_time_interval:.3f}秒")
    print(f"  每帧扫描角度: {degrees_per_frame:.1f}度")
    print(f"  总帧数: {num_frames}")

    for frame in range(num_frames):
        current_time = frame * frame_time_interval
        current_beam_angle = (frame * degrees_per_frame) % 360.0  # 当前波束角度
        points = []

        for i in range(num_targets):
            # 目标运动模型 - 匀速直线运动 + 轻微机动
            maneuver = 2.0 * np.sin(current_time * 0.1 + i) * np.array([1, 1, 0.1])
            pos = (base_positions[i] +
                   base_velocities[i] * current_time +
                   maneuver * current_time)

            # 计算目标相对雷达的方位角
            target_azimuth = np.degrees(np.arctan2(pos[1], pos[0])) % 360
            target_range = np.linalg.norm(pos[:2])  # 水平距离

            # 雷达波束覆盖检查
            beam_start = current_beam_angle
            beam_end = (current_beam_angle + beam_width) % 360

            # 处理跨越0度的情况
            if beam_end < beam_start:  # 跨越0度
                in_beam = (target_azimuth >= beam_start) or (target_azimuth <= beam_end)
            else:
                in_beam = beam_start <= target_azimuth <= beam_end

            # 距离检查
            in_range = target_range <= detection_range

            # 高检测概率模型（用于测试）
            if in_beam and in_range:
                # 非常高的基础检测概率
                base_prob = 0.99

                # 很小的距离衰减
                range_factor = max(0.9, 1.0 - target_range / detection_range * 0.1)

                # 很小的波束边缘衰减
                beam_center = (beam_start + beam_width/2) % 360
                angle_diff = min(abs(target_azimuth - beam_center),
                               360 - abs(target_azimuth - beam_center))
                beam_factor = max(0.95, 1.0 - angle_diff / (beam_width/2 + 1.0))  # 增加1度容差

                detection_prob = base_prob * range_factor * beam_factor

                if np.random.rand() < detection_prob:
                    # 添加测量噪声
                    noise = np.random.normal(0, noise_std, size=3)
                    # 距离噪声与距离成正比
                    range_noise_factor = 1 + target_range / 1000.0
                    noise[:2] *= range_noise_factor

                    noisy_pos = pos + noise
                    points.append(np.hstack((noisy_pos, current_time, i)))

        # 添加杂波 - 只在当前波束扇区内（适中密度）
        clutter_density = 0.3  # 每度杂波密度 - 适中
        expected_clutter = int(beam_width * clutter_density)
        num_clutter = np.random.poisson(expected_clutter)

        for _ in range(num_clutter):
            # 在当前波束扇区内随机生成杂波
            clutter_azimuth = np.random.uniform(current_beam_angle,
                                              current_beam_angle + beam_width) % 360
            clutter_range = np.random.uniform(50, detection_range)
            clutter_elevation = np.random.uniform(-50, 50)

            # 转换为笛卡尔坐标
            clutter_x = clutter_range * np.cos(np.radians(clutter_azimuth))
            clutter_y = clutter_range * np.sin(np.radians(clutter_azimuth))
            clutter_z = clutter_elevation

            clutter_pos = np.array([clutter_x, clutter_y, clutter_z])
            points.append(np.hstack((clutter_pos, current_time, -1)))  # -1表示杂波

        data.append(np.array(points) if points else np.empty((0, 5)))

        # 每30帧打印一次进度和调试信息
        if frame % 30 == 0:
            target_info = []
            for i in range(num_targets):
                current_time = frame * frame_time_interval
                maneuver = 2.0 * np.sin(current_time * 0.1 + i) * np.array([1, 1, 0.1])
                pos = (base_positions[i] +
                       base_velocities[i] * current_time +
                       maneuver * current_time)
                target_range = np.linalg.norm(pos[:2])
                target_azimuth = np.degrees(np.arctan2(pos[1], pos[0])) % 360
                target_info.append(f"T{i}:r={target_range:.0f}m,az={target_azimuth:.0f}°")

            print(f"生成帧 {frame}/{num_frames}, 波束角度: {current_beam_angle:.1f}°, "
                  f"检测点数: {len(points)}")
            print(f"  目标状态: {' '.join(target_info)}")

    # 创建帧信息字典（与CSV读取函数保持一致）
    frame_info = {}
    for frame_idx in range(num_frames):
        current_time = frame_idx * frame_time_interval
        current_beam_angle = (frame_idx * degrees_per_frame) % 360.0
        frame_info[frame_idx] = {
            'time': current_time,
            'beam_angle': current_beam_angle,
            'num_detections': len(data[frame_idx]) if frame_idx < len(data) else 0,
            'num_targets': len([p for p in data[frame_idx] if len(p) > 4 and p[4] != -1]) if frame_idx < len(data) else 0,
            'num_clutter': len([p for p in data[frame_idx] if len(p) > 4 and p[4] == -1]) if frame_idx < len(data) else 0
        }

    return data, frame_info

def cluster_detections(points, eps=0.5, min_samples=1):
    """DBSCAN聚类检测点"""
    if len(points) == 0:
        return np.empty((0, 5))
    
    # 标准化坐标后聚类
    scaled = StandardScaler().fit_transform(points[:, :3])
    labels = DBSCAN(eps=eps, min_samples=min_samples).fit_predict(scaled)
    
    # 合并聚类点
    clustered = []
    for lbl in np.unique(labels):
        if lbl == -1:  # 噪声点跳过
            continue
        cluster = points[labels == lbl]
        centroid = np.mean(cluster[:, :3], axis=0)
        timestamp = np.min(cluster[:, 3])  # 取最早时间戳
        clustered.append(np.hstack((centroid, timestamp)))
    
    return np.array(clustered) if clustered else np.empty((0, 4))

# ---------- 改进的跟踪器 ----------
class ImprovedKalmanTracker:
    id_counter = 0

    def __init__(self, point):
        """初始化跟踪器，使用匀加速模型（基于tracker.py优化）"""
        self.last_time = point[3] if len(point) > 3 else 0

        # 使用匀加速模型（9维状态：位置、速度、加速度）
        self.kf = KalmanFilter(dim_x=9, dim_z=3)
        self._init_ca_filter()

        # 初始化状态向量：[x, y, z, vx, vy, vz, ax, ay, az]
        self.kf.x[:3] = point[:3].reshape(-1, 1)
        # 初始速度和加速度设为0（保守估计）
        self.kf.x[3:6] = 0  # 初始速度为0
        self.kf.x[6:9] = 0  # 初始加速度为0

        # 采用tracker.py的管理策略
        self.id = None  # 延迟赋ID
        self.time_since_update = 0
        self.hit_streak = 1
        self.age = 1
        self.history = []
        self.prediction_history = []

    def _init_ca_filter(self):
        """初始化匀加速模型滤波器（参考tracker.py参数优化）"""
        dt = 1.0  # 初始值，后续动态更新

        # 状态转移矩阵 F (9x9) - 匀加速模型
        # 状态向量: [x, y, z, vx, vy, vz, ax, ay, az]
        self.kf.F = np.array([
            [1,0,0, dt,0,0, 0.5*dt**2,0,0],
            [0,1,0, 0,dt,0, 0,0.5*dt**2,0],
            [0,0,1, 0,0,dt, 0,0,0.5*dt**2],
            [0,0,0, 1,0,0, dt,0,0],
            [0,0,0, 0,1,0, 0,dt,0],
            [0,0,0, 0,0,1, 0,0,dt],
            [0,0,0, 0,0,0, 1,0,0],
            [0,0,0, 0,0,0, 0,1,0],
            [0,0,0, 0,0,0, 0,0,1]
        ])

        # 观测矩阵 H (3x9) - 只观测位置
        self.kf.H = np.eye(3, 9)

        # 观测噪声协方差矩阵 R (3x3) - 参考tracker.py，适度调整
        self.kf.R = np.eye(3) * 10.0  # 与tracker.py保持一致的观测噪声

        # 初始状态协方差矩阵 P (9x9) - 参考tracker.py，扩展到9维
        self.kf.P = np.diag([10, 10, 10,    # 位置不确定性（与tracker.py一致）
                            10, 10, 10,     # 速度不确定性
                            1, 1, 1])       # 加速度不确定性（较小）

        # 过程噪声协方差矩阵 Q (9x9) - 参考tracker.py的保守设置
        self.kf.Q = np.diag([0.1, 0.1, 0.1,    # 位置过程噪声（与tracker.py一致）
                            0.1, 0.1, 0.1,     # 速度过程噪声
                            0.01, 0.01, 0.01]) # 加速度过程噪声（很小）
    


    def predict(self, current_time=None):
        """预测下一状态（匀加速模型，参考tracker.py简化）"""
        # 如果提供了时间，更新时间步长
        if current_time is not None:
            dt = max(0.05, current_time - self.last_time)  # 最小0.05秒防止数值问题

            # 更新状态转移矩阵的时间相关项
            self.kf.F[0,3] = dt
            self.kf.F[1,4] = dt
            self.kf.F[2,5] = dt
            self.kf.F[0,6] = 0.5*dt**2
            self.kf.F[1,7] = 0.5*dt**2
            self.kf.F[2,8] = 0.5*dt**2
            self.kf.F[3,6] = dt
            self.kf.F[4,7] = dt
            self.kf.F[5,8] = dt

            self.last_time = current_time

        # 执行预测（保持tracker.py的简洁性）
        self.kf.predict()

        # 更新跟踪器状态（与tracker.py一致）
        self.time_since_update += 1
        self.age += 1

        pred = self.kf.x[:3].flatten()
        self.history.append(pred)
        return pred

    def update(self, point):
        """量测更新（与tracker.py保持一致的接口）"""
        # 使用观测到的位置更新滤波器
        self.kf.update(point[:3].reshape(-1, 1))

        # 重置状态（与tracker.py一致）
        self.time_since_update = 0
        self.hit_streak += 1

        # 更新时间戳
        self.last_time = point[3] if len(point) > 3 else self.last_time
    
    def assign_id(self):
        if self.id is None:
            self.id = ImprovedKalmanTracker.id_counter
            ImprovedKalmanTracker.id_counter += 1

class RadarTracker:
    def __init__(self, scan_period=6, beam_width=3):
        """雷达专用跟踪器，采用tracker.py的成熟策略"""
        self.scan_period = scan_period
        self.beam_width = beam_width
        self.frame_interval = scan_period / (360/beam_width)  # 每帧时间间隔

        # 平衡精确度和召回率的参数设置
        self.max_age = 5                    # 适中的生存时间
        self.distance_threshold = 120        # 适中的关联阈值
        self.reid_age = 25                  # 适中的重关联时间
        self.min_hits = 2                   # 恢复到tracker.py的设置，减少误检

        # tracker.py的核心组件
        self.trackers = []
        self.suspended_trackers = []
        self.frame_count = 0
    
    def update(self, detections):
        """更新跟踪器（采用tracker.py的核心逻辑）"""
        self.frame_count += 1

        # 预测现有跟踪器（简化时间处理）
        preds = np.array([trk.predict() for trk in self.trackers])

        # 数据关联（采用tracker.py的简洁策略）
        matched, unmatched_dets, unmatched_trks = self.match(detections, preds)

        # 更新匹配的跟踪器（与tracker.py一致）
        for m_det, m_trk in matched:
            self.trackers[m_trk].update(detections[m_det])

        # 管理跟踪器生命周期（采用tracker.py逻辑）
        self._manage_trackers_v2(detections, unmatched_dets)

        # 返回确认的跟踪结果（与tracker.py一致）
        return self._get_confirmed_tracks_v2()

    def match(self, detections, predictions):
        """数据关联（直接采用tracker.py的实现）"""
        # 如果预测为空（无跟踪器），直接返回所有检测为未匹配
        if len(predictions) == 0:
            return [], np.arange(len(detections)), []

        # 计算所有预测与检测点迹之间的欧式距离
        dists = np.linalg.norm(predictions[:, None] - detections[:, :3], axis=2)

        # 匈牙利算法计算最优匹配
        trk_idx, det_idx = linear_sum_assignment(dists)

        matched = []
        unmatched_dets = list(range(len(detections)))
        unmatched_trks = list(range(len(predictions)))

        # 根据距离阈值筛选有效匹配
        for t, d in zip(trk_idx, det_idx):
            if dists[t, d] < self.distance_threshold:
                matched.append((d, t))
                unmatched_dets.remove(d)
                unmatched_trks.remove(t)
        return matched, unmatched_dets, unmatched_trks

    def _manage_trackers_v2(self, detections, unmatched_dets):
        """管理跟踪器生命周期（采用tracker.py逻辑）"""
        # --- 清理和转移失活追踪器 ---
        new_trackers = []
        for trk in self.trackers:
            if trk.time_since_update <= self.max_age:
                # 活跃跟踪器，保留在主列表
                new_trackers.append(trk)
            elif trk.time_since_update <= self.reid_age:
                # 短暂失活，转移到暂挂列表
                self.suspended_trackers.append(trk)
            # 超过reid_age的跟踪器会被自动丢弃
        self.trackers = new_trackers # 更新主跟踪器列表

        # --- reid 匹配恢复追踪器 ---
        reid_matched, reid_dets, _ = self.reid_match(detections[unmatched_dets])
        for det_idx, old_trk in reid_matched:
            # 重置暂挂跟踪器状态
            old_trk.kf.x[:3] = detections[unmatched_dets[det_idx]][:3].reshape(-1, 1) # 更新位置
            old_trk.time_since_update = 0   # 重置未更新时间
            old_trk.hit_streak = 2          # 重置连续命中计数
            old_trk.age = 1                 # 重置年龄
            old_trk.last_time = detections[unmatched_dets[det_idx]][3] if len(detections[unmatched_dets[det_idx]]) > 3 else old_trk.last_time
            self.trackers.append(old_trk)   # 移回主跟踪器列表

        # --- 未匹配的检测创建新追踪器 ---
        for idx in reid_dets:
            self.trackers.append(ImprovedKalmanTracker(detections[unmatched_dets[idx]]))

    def reid_match(self, new_detections):
        """重新匹配（直接采用tracker.py的实现）"""
        # 如果暂挂跟踪器或新检测为空，直接返回
        if len(self.suspended_trackers) == 0 or len(new_detections) == 0:
            return [], list(range(len(new_detections))), []

        # 获取暂挂跟踪器的预测位置
        old_preds = np.array([trk.kf.x[:3].flatten() for trk in self.suspended_trackers])

        # 计算距离矩阵（暂挂跟踪器 x 新检测）
        dists = np.linalg.norm(old_preds[:, None] - new_detections[:, :3], axis=2)

        # 匈牙利算法匹配
        trk_idx, det_idx = linear_sum_assignment(dists)

        matched = []
        unmatched_dets = list(range(len(new_detections)))
        for t, d in zip(trk_idx, det_idx):
            if dists[t, d] < self.distance_threshold * 2:
                matched.append((d, self.suspended_trackers[t]))
                unmatched_dets.remove(d)
        return matched, unmatched_dets, []

    def _get_confirmed_tracks_v2(self):
        """获取确认的跟踪目标（采用tracker.py逻辑）"""
        results = []
        for trk in self.trackers:
            if trk.time_since_update < 1 and (trk.hit_streak >= self.min_hits or self.frame_count <= self.min_hits):
                if trk.id is None and trk.hit_streak >= self.min_hits:
                    trk.assign_id()  # 分配 ID 只发生一次
                if trk.id is not None:
                    pred = trk.kf.x[:3].flatten()
                    results.append(np.concatenate([pred, [trk.id]]))
        return np.array(results) if results else np.empty((0, 4))

    # 保留旧方法以防需要回退，但不再使用
    def _match_old(self, detections, predictions):
        """旧的复杂匹配方法（已弃用，使用match方法）"""
        # 这个方法已被简化的match方法替代
        return self.match(detections, predictions)
    
    # 旧的管理方法（已弃用，使用_manage_trackers_v2）
    def _manage_trackers_old(self, detections, unmatched_dets, unmatched_trks, current_time):
        """旧的复杂管理方法（已弃用）"""
        # 这个方法已被简化的_manage_trackers_v2方法替代
        pass

# ---------- 跟踪性能评估 ----------
def calculate_tracking_metrics(frames, tracked_history, association_threshold=50.0):
    """
    计算跟踪性能指标

    参数:
    - frames: 真实数据帧列表
    - tracked_history: 跟踪结果历史
    - association_threshold: 关联阈值(米)

    返回:
    - metrics: 包含各种性能指标的字典
    """
    # 提取真实轨迹
    true_trajectories = {}
    for frame_idx, frame in enumerate(frames):
        if len(frame) > 0:
            for point in frame:
                if len(point) >= 5 and point[4] != -1:  # 真实目标
                    tid = int(point[4])
                    if tid not in true_trajectories:
                        true_trajectories[tid] = []
                    true_trajectories[tid].append({
                        'frame': frame_idx,
                        'pos': point[:3],
                        'time': point[3]
                    })

    # 提取跟踪轨迹
    track_trajectories = {}
    for frame_idx, tracked in enumerate(tracked_history):
        if len(tracked) > 0:
            for track in tracked:
                tid = int(track[3])
                if tid not in track_trajectories:
                    track_trajectories[tid] = []
                track_trajectories[tid].append({
                    'frame': frame_idx,
                    'pos': track[:3],
                    'time': frame_idx * 0.05  # 假设帧间隔0.05秒
                })

    # 计算关联矩阵和性能指标
    total_true_detections = 0
    total_tracked_detections = len(tracked_history)
    correctly_tracked = 0
    false_positives = 0
    missed_detections = 0
    position_errors = []

    # 逐帧分析
    for frame_idx in range(len(frames)):
        # 获取该帧的真实目标
        true_frame = {}
        if frame_idx < len(frames) and len(frames[frame_idx]) > 0:
            for point in frames[frame_idx]:
                if len(point) >= 5 and point[4] != -1:
                    true_frame[int(point[4])] = point[:3]

        total_true_detections += len(true_frame)

        # 获取该帧的跟踪结果
        tracked_frame = {}
        if frame_idx < len(tracked_history) and len(tracked_history[frame_idx]) > 0:
            for track in tracked_history[frame_idx]:
                tracked_frame[int(track[3])] = track[:3]

        # 计算关联
        used_tracks = set()
        for true_id, true_pos in true_frame.items():
            best_match = None
            best_distance = float('inf')

            for track_id, track_pos in tracked_frame.items():
                if track_id in used_tracks:
                    continue
                distance = np.linalg.norm(true_pos - track_pos)
                if distance < best_distance and distance < association_threshold:
                    best_distance = distance
                    best_match = track_id

            if best_match is not None:
                correctly_tracked += 1
                position_errors.append(best_distance)
                used_tracks.add(best_match)
            else:
                missed_detections += 1

        # 计算假阳性
        false_positives += len(tracked_frame) - len(used_tracks)

    # 计算性能指标
    precision = correctly_tracked / (correctly_tracked + false_positives) if (correctly_tracked + false_positives) > 0 else 0
    recall = correctly_tracked / total_true_detections if total_true_detections > 0 else 0
    f1_score = 2 * precision * recall / (precision + recall) if (precision + recall) > 0 else 0

    # MOTA (Multiple Object Tracking Accuracy)
    mota = 1 - (missed_detections + false_positives) / total_true_detections if total_true_detections > 0 else 0

    # MOTP (Multiple Object Tracking Precision)
    motp = np.mean(position_errors) if position_errors else float('inf')

    metrics = {
        'precision': precision,
        'recall': recall,
        'f1_score': f1_score,
        'mota': mota,
        'motp': motp,
        'total_true_detections': total_true_detections,
        'correctly_tracked': correctly_tracked,
        'false_positives': false_positives,
        'missed_detections': missed_detections,
        'avg_position_error': motp,
        'true_trajectories': true_trajectories,
        'track_trajectories': track_trajectories
    }

    return metrics

def print_tracking_metrics(metrics):
    """打印跟踪性能指标"""
    print("\n" + "="*50)
    print("跟踪性能评估结果")
    print("="*50)
    print(f"精确度 (Precision): {metrics['precision']:.3f}")
    print(f"召回率 (Recall): {metrics['recall']:.3f}")
    print(f"F1分数: {metrics['f1_score']:.3f}")
    print(f"MOTA (多目标跟踪精度): {metrics['mota']:.3f}")
    print(f"MOTP (多目标跟踪准确度): {metrics['motp']:.2f} 米")
    print(f"平均位置误差: {metrics['avg_position_error']:.2f} 米")
    print("-"*50)
    print(f"总真实检测数: {metrics['total_true_detections']}")
    print(f"正确跟踪数: {metrics['correctly_tracked']}")
    print(f"漏检数: {metrics['missed_detections']}")
    print(f"误检数: {metrics['false_positives']}")
    print("-"*50)
    print(f"真实轨迹数: {len(metrics['true_trajectories'])}")
    print(f"跟踪轨迹数: {len(metrics['track_trajectories'])}")
    print("="*50)

# ---------- 增强可视化 ----------
def visualize_tracking_comparison(frames, tracked_history, metrics=None, save=False):
    """可视化跟踪效果对比：真实轨迹 vs 跟踪轨迹"""
    fig = plt.figure(figsize=(16, 10))

    # 创建子图
    ax1 = fig.add_subplot(221, projection='3d')  # 3D总览
    ax2 = fig.add_subplot(222)                   # XY平面
    ax3 = fig.add_subplot(223)                   # 跟踪统计
    ax4 = fig.add_subplot(224)                   # 误差分析

    colors = plt.cm.tab10(np.linspace(0, 1, 10))
    target_colors = {}
    track_colors = {}

    # 提取所有真实目标轨迹
    true_trajectories = {}
    for frame_idx, frame in enumerate(frames):
        if len(frame) > 0:
            for point in frame:
                if len(point) >= 5 and point[4] != -1:  # 真实目标
                    tid = int(point[4])
                    if tid not in true_trajectories:
                        true_trajectories[tid] = []
                        target_colors[tid] = colors[tid % len(colors)]
                    true_trajectories[tid].append({
                        'frame': frame_idx,
                        'pos': point[:3],
                        'time': point[3] if len(point) > 3 else frame_idx * 0.05
                    })

    # 提取所有跟踪轨迹
    track_trajectories = {}
    for frame_idx, tracked in enumerate(tracked_history):
        if len(tracked) > 0:
            for track in tracked:
                tid = int(track[3])
                if tid not in track_trajectories:
                    track_trajectories[tid] = []
                    track_colors[tid] = colors[tid % len(colors)]
                track_trajectories[tid].append({
                    'frame': frame_idx,
                    'pos': track[:3],
                    'time': frame_idx * 0.05
                })

    return fig, (ax1, ax2, ax3, ax4), true_trajectories, track_trajectories, target_colors, track_colors

def visualize(frames, tracked_history, save=False):
    """增强的可视化函数 - 包含性能评估"""
    # 计算性能指标
    metrics = calculate_tracking_metrics(frames, tracked_history)

    fig, axes, true_traj, track_traj, target_colors, track_colors = visualize_tracking_comparison(frames, tracked_history, metrics, save)
    ax1, ax2, ax3, ax4 = axes

    # 绘制3D总览
    ax1.set_title('3D Tracking Overview')
    ax1.set_xlim(-800, 800)
    ax1.set_ylim(-800, 800)
    ax1.set_zlim(-100, 100)
    ax1.set_xlabel('X (m)')
    ax1.set_ylabel('Y (m)')
    ax1.set_zlabel('Z (m)')

    # 绘制真实轨迹
    for tid, trajectory in true_traj.items():
        if len(trajectory) > 1:
            positions = np.array([t['pos'] for t in trajectory])
            ax1.plot(positions[:,0], positions[:,1], positions[:,2],
                    '--', color=target_colors[tid], linewidth=2, alpha=0.7,
                    label=f'True Target {tid}')

    # 绘制跟踪轨迹
    for tid, trajectory in track_traj.items():
        if len(trajectory) > 1:
            positions = np.array([t['pos'] for t in trajectory])
            ax1.plot(positions[:,0], positions[:,1], positions[:,2],
                    '-', color=track_colors[tid], linewidth=3, alpha=0.9,
                    label=f'Tracked {tid}')

    ax1.legend(bbox_to_anchor=(1.05, 1), loc='upper left')

    # 绘制XY平面视图
    ax2.set_title('XY Plane View')
    ax2.set_xlim(-800, 800)
    ax2.set_ylim(-800, 800)
    ax2.set_xlabel('X (m)')
    ax2.set_ylabel('Y (m)')
    ax2.grid(True, alpha=0.3)
    ax2.set_aspect('equal')

    # XY平面真实轨迹
    for tid, trajectory in true_traj.items():
        if len(trajectory) > 1:
            positions = np.array([t['pos'] for t in trajectory])
            ax2.plot(positions[:,0], positions[:,1],
                    '--', color=target_colors[tid], linewidth=2, alpha=0.7)
            # 标记起点和终点
            ax2.scatter(positions[0,0], positions[0,1],
                       color=target_colors[tid], marker='o', s=50, alpha=0.8)
            ax2.scatter(positions[-1,0], positions[-1,1],
                       color=target_colors[tid], marker='s', s=50, alpha=0.8)

    # XY平面跟踪轨迹
    for tid, trajectory in track_traj.items():
        if len(trajectory) > 1:
            positions = np.array([t['pos'] for t in trajectory])
            ax2.plot(positions[:,0], positions[:,1],
                    '-', color=track_colors[tid], linewidth=3, alpha=0.9)

    # 跟踪统计和性能指标
    ax3.set_title('Tracking Performance Metrics')

    # 使用传入的metrics或计算新的metrics
    if metrics is None:
        # 计算基本统计
        total_true_targets = len(true_traj)
        total_tracks = len(track_traj)

        # 计算每个目标的跟踪覆盖率
        coverage_stats = []
        for tid in true_traj.keys():
            true_frames = len(true_traj[tid])
            tracked_frames = len(track_traj.get(tid, []))
            coverage = tracked_frames / true_frames if true_frames > 0 else 0
            coverage_stats.append(coverage)

        avg_coverage = np.mean(coverage_stats) if coverage_stats else 0

        stats_text = f"""basic stats:
real targets: {total_true_targets}
tracked targets: {total_tracks}
average coverage: {avg_coverage:.2%}

coverage details:"""

        for tid, coverage in zip(true_traj.keys(), coverage_stats):
            stats_text += f"\ntarget {tid}: {coverage:.2%}"
    else:
        # 显示详细的性能指标
        stats_text = f"""performance metrics:
precision: {metrics['precision']:.3f}
recall: {metrics['recall']:.3f}
F1: {metrics['f1_score']:.3f}
MOTA: {metrics['mota']:.3f}
MOTP: {metrics['motp']:.1f}m

Statistical Information:
True Detections: {metrics['total_true_detections']}
Correctly Tracked: {metrics['correctly_tracked']}
Missed Detections: {metrics['missed_detections']}
False Positives: {metrics['false_positives']}

Trajectory Count:
True Trajectories: {len(metrics['true_trajectories'])}
Tracked Trajectories: {len(metrics['track_trajectories'])}"""

    ax3.text(0.05, 0.95, stats_text, transform=ax3.transAxes,
             verticalalignment='top', fontfamily='monospace', fontsize=9)
    ax3.set_xlim(0, 1)
    ax3.set_ylim(0, 1)
    ax3.axis('off')

    # 误差分析
    ax4.set_title('Tracking Error Analysis')

    # 计算跟踪误差（当真实目标和跟踪目标都存在时）
    frame_errors = []
    for frame_idx in range(len(frames)):
        frame_error = 0
        error_count = 0

        # 获取该帧的真实目标
        true_targets_frame = {}
        if frame_idx < len(frames) and len(frames[frame_idx]) > 0:
            for point in frames[frame_idx]:
                if len(point) >= 5 and point[4] != -1:
                    true_targets_frame[int(point[4])] = point[:3]

        # 获取该帧的跟踪结果
        tracked_frame = {}
        if frame_idx < len(tracked_history) and len(tracked_history[frame_idx]) > 0:
            for track in tracked_history[frame_idx]:
                tracked_frame[int(track[3])] = track[:3]

        # 计算匹配目标的误差
        for tid in true_targets_frame.keys():
            if tid in tracked_frame:
                error = np.linalg.norm(true_targets_frame[tid] - tracked_frame[tid])
                frame_error += error
                error_count += 1

        avg_frame_error = frame_error / error_count if error_count > 0 else 0
        frame_errors.append(avg_frame_error)

    ax4.plot(frame_errors, 'b-', alpha=0.7, linewidth=1)
    ax4.set_xlabel('Frame')
    ax4.set_ylabel('Average Error (m)')
    ax4.grid(True, alpha=0.3)

    if frame_errors:
        ax4.set_ylim(0, max(max(frame_errors), 10))
        avg_error = np.mean([e for e in frame_errors if e > 0])
        ax4.axhline(y=avg_error, color='r', linestyle='--', alpha=0.7,
                   label=f'Avg Error: {avg_error:.1f}m')
        ax4.legend()

    plt.tight_layout()

    if save:
        plt.savefig('tracking_comparison.png', dpi=150, bbox_inches='tight')
        print("跟踪对比图已保存到: tracking_comparison.png")

    plt.show()
    return fig

def visualize_animated_3d(frames, tracked_results, max_trace_length=100, save_animation=False):
    """
    动态3D可视化函数 - 逐帧显示跟踪效果

    参数:
    - frames: 原始帧数据
    - tracked_results: 跟踪结果
    - max_trace_length: 最大轨迹长度
    - save_animation: 是否保存动画
    """
    fig = plt.figure(figsize=(12, 9))
    ax = fig.add_subplot(111, projection='3d')

    # 颜色和轨迹历史
    colors = {}
    trace_histories = {}
    true_tracks = {}

    # 预处理真实轨迹数据
    for frame_idx, frame in enumerate(frames):
        for pt in frame:
            if len(pt) < 5:
                continue
            tid = int(pt[4])
            if tid == -1:
                continue
            true_tracks.setdefault(tid, []).append((frame_idx, pt[:3]))

    print(f"开始动态3D可视化，共 {len(frames)} 帧...")
    print("按任意键继续到下一帧，按 'q' 退出")

    # 逐帧动画
    for frame_idx, (raw, tracked) in enumerate(zip(frames, tracked_results)):
        ax.clear()
        ax.set_title(f'Frame {frame_idx} - Radar Tracking Visualization', fontsize=14)
        ax.set_xlim(-1000, 1000)
        ax.set_ylim(-1000, 1000)
        ax.set_zlim(-100, 100)
        ax.set_xlabel('X (m)')
        ax.set_ylabel('Y (m)')
        ax.set_zlabel('Z (m)')

        # 显示原始检测点（灰色）
        if len(raw) > 0:
            ax.scatter(raw[:, 0], raw[:, 1], raw[:, 2],
                      c='gray', marker='o', alpha=0.3, s=20, label='Raw Detections')

        # 显示真实轨迹（红色虚线）
        for tid, track_data in true_tracks.items():
            # 获取到当前帧为止的轨迹点
            current_track = [data[1] for data in track_data if data[0] <= frame_idx]
            if len(current_track) < 2:
                continue

            track = np.array(current_track)
            # 绘制完整轨迹（红色虚线）
            ax.plot(track[:, 0], track[:, 1], track[:, 2],
                   color='red', linestyle='--', linewidth=2, alpha=0.8)

            # 标记当前位置（红色三角形）
            if len(current_track) > 0:
                current_pos = current_track[-1]
                ax.scatter(current_pos[0], current_pos[1], current_pos[2],
                          c='red', marker='^', s=60, alpha=0.9)

        # 显示跟踪结果和轨迹
        for pt in tracked:
            tid = int(pt[3])

            # 为每个跟踪ID分配颜色
            if tid not in colors:
                colors[tid] = np.random.rand(3,)

            # 更新轨迹历史
            if tid not in trace_histories:
                trace_histories[tid] = []
            trace_histories[tid].append((frame_idx, pt[:3]))

            # 保持轨迹长度限制
            trace_histories[tid] = [x for x in trace_histories[tid]
                                  if frame_idx - x[0] <= max_trace_length]

            # 绘制跟踪轨迹
            if len(trace_histories[tid]) > 1:
                trace = np.array([x[1] for x in trace_histories[tid]])
                ax.plot(trace[:, 0], trace[:, 1], trace[:, 2],
                       color=colors[tid], linewidth=3, alpha=0.9)

            # 标记当前跟踪位置
            ax.scatter(pt[0], pt[1], pt[2],
                      c=[colors[tid]], marker='x', s=80, linewidth=3)

            # 添加ID标签
            ax.text(pt[0], pt[1], pt[2] + 20, f'ID{tid}',
                   color=colors[tid], fontsize=10, fontweight='bold')

        # 添加图例
        legend_elements = []
        if len(raw) > 0:
            legend_elements.append(plt.Line2D([0], [0], marker='o', color='w',
                                            markerfacecolor='gray', markersize=8,
                                            alpha=0.3, label='Raw Detections'))
        legend_elements.append(plt.Line2D([0], [0], color='red', linestyle='--',
                                        linewidth=2, label='True Tracks'))
        legend_elements.append(plt.Line2D([0], [0], marker='^', color='w',
                                        markerfacecolor='red', markersize=10,
                                        label='True Targets'))
        legend_elements.append(plt.Line2D([0], [0], marker='x', color='w',
                                        markerfacecolor='blue', markersize=10,
                                        label='Tracked Targets'))

        ax.legend(handles=legend_elements, loc='upper right')

        # 添加统计信息
        stats_text = f"Frame: {frame_idx}\n"
        stats_text += f"Raw Detections: {len(raw)}\n"
        stats_text += f"Tracked Targets: {len(tracked)}\n"
        stats_text += f"True Targets: {len([pt for pt in raw if len(pt) >= 5 and pt[4] != -1])}"

        ax.text2D(0.02, 0.98, stats_text, transform=ax.transAxes,
                 verticalalignment='top', fontfamily='monospace',
                 bbox=dict(boxstyle='round', facecolor='wheat', alpha=0.8))

        plt.draw()
        plt.pause(0.1)

        # 等待用户输入
        if frame_idx < len(frames) - 1:
            user_input = input(f"Frame {frame_idx} 完成。按 Enter 继续，输入 'q' 退出: ")
            if user_input.lower() == 'q':
                break

    print("动态可视化完成")
    plt.show()
    return fig

def interpolate_trajectory(track_history, interpolation_points=10, method='cubic'):
    """
    对跟踪轨迹进行插值，生成更平滑的轨迹

    参数:
    - track_history: 跟踪历史数据，格式为 [(frame_idx, position), ...]
    - interpolation_points: 在每两个跟踪点之间插入的点数
    - method: 插值方法 ('linear', 'cubic', 'kalman')

    返回:
    - interpolated_trajectory: 插值后的轨迹点列表
    """
    if len(track_history) < 2:
        return track_history

    # 提取时间和位置数据
    times = np.array([item[0] for item in track_history])
    positions = np.array([item[1] for item in track_history])

    if method == 'kalman':
        # 使用卡尔曼滤波器进行预测插值
        return _kalman_interpolate(track_history, interpolation_points)

    elif method in ['linear', 'cubic']:
        # 使用scipy插值
        if len(track_history) < 4 and method == 'cubic':
            method = 'linear'  # 点数不足时降级为线性插值

        interpolated_trajectory = []

        for i in range(len(track_history) - 1):
            # 添加当前点
            interpolated_trajectory.append(track_history[i])

            # 在当前点和下一点之间插值
            t1, pos1 = track_history[i]
            t2, pos2 = track_history[i + 1]

            # 生成插值时间点
            t_interp = np.linspace(t1, t2, interpolation_points + 2)[1:-1]  # 排除端点

            # 对每个维度进行插值
            interp_positions = []
            for dim in range(3):  # x, y, z
                if method == 'linear':
                    interp_func = interp1d([t1, t2], [pos1[dim], pos2[dim]], kind='linear')
                else:  # cubic
                    # 需要更多点进行三次插值，使用周围的点
                    start_idx = max(0, i - 1)
                    end_idx = min(len(track_history), i + 3)
                    t_extended = times[start_idx:end_idx]
                    pos_extended = positions[start_idx:end_idx, dim]

                    if len(t_extended) >= 4:
                        interp_func = interp1d(t_extended, pos_extended, kind='cubic',
                                             bounds_error=False, fill_value='extrapolate')
                    else:
                        interp_func = interp1d([t1, t2], [pos1[dim], pos2[dim]], kind='linear')

                dim_interp = interp_func(t_interp)
                interp_positions.append(dim_interp)

            # 添加插值点
            for j, t in enumerate(t_interp):
                interp_pos = np.array([interp_positions[0][j],
                                     interp_positions[1][j],
                                     interp_positions[2][j]])
                interpolated_trajectory.append((t, interp_pos))

        # 添加最后一个点
        interpolated_trajectory.append(track_history[-1])

        return interpolated_trajectory

    else:
        raise ValueError(f"不支持的插值方法: {method}")

def _kalman_interpolate(track_history, interpolation_points):
    """使用卡尔曼滤波器进行轨迹插值"""
    if len(track_history) < 2:
        return track_history

    # 初始化卡尔曼滤波器用于插值
    kf = KalmanFilter(dim_x=6, dim_z=3)  # 6维状态：位置+速度

    # 状态转移矩阵（匀速模型）
    dt = 1.0
    kf.F = np.array([
        [1, 0, 0, dt, 0, 0],
        [0, 1, 0, 0, dt, 0],
        [0, 0, 1, 0, 0, dt],
        [0, 0, 0, 1, 0, 0],
        [0, 0, 0, 0, 1, 0],
        [0, 0, 0, 0, 0, 1]
    ])

    # 观测矩阵
    kf.H = np.eye(3, 6)

    # 噪声协方差
    kf.R = np.eye(3) * 10
    kf.Q = np.eye(6) * 5
    kf.P = np.eye(6) * 100

    # 初始化状态
    first_pos = track_history[0][1]
    if len(track_history) > 1:
        second_pos = track_history[1][1]
        dt_init = track_history[1][0] - track_history[0][0]
        init_vel = (second_pos - first_pos) / dt_init if dt_init > 0 else np.zeros(3)
    else:
        init_vel = np.zeros(3)

    kf.x = np.concatenate([first_pos, init_vel]).reshape(-1, 1)

    interpolated_trajectory = []

    for i in range(len(track_history) - 1):
        # 添加当前观测点
        interpolated_trajectory.append(track_history[i])

        # 更新卡尔曼滤波器
        t1, pos1 = track_history[i]
        t2, pos2 = track_history[i + 1]
        dt_real = t2 - t1

        kf.update(pos1.reshape(-1, 1))

        # 在两点之间进行插值预测
        dt_interp = dt_real / (interpolation_points + 1)

        for j in range(interpolation_points):
            # 更新状态转移矩阵的时间步长
            kf.F[0, 3] = dt_interp
            kf.F[1, 4] = dt_interp
            kf.F[2, 5] = dt_interp

            # 预测下一个状态
            kf.predict()

            # 添加插值点
            t_interp = t1 + (j + 1) * dt_interp
            pos_interp = kf.x[:3].flatten()
            interpolated_trajectory.append((t_interp, pos_interp))

    # 添加最后一个点
    interpolated_trajectory.append(track_history[-1])

    return interpolated_trajectory

def smooth_tracking_results(tracked_history, frame_info, interpolation_points=5, method='cubic'):
    """
    对跟踪结果进行平滑处理，特别适用于120帧分组输入的情况

    参数:
    - tracked_history: 跟踪历史结果
    - frame_info: 帧信息，包含时间等
    - interpolation_points: 插值点数
    - method: 插值方法

    返回:
    - smoothed_results: 平滑后的跟踪结果
    """
    print(f"开始轨迹平滑处理，插值方法: {method}, 插值点数: {interpolation_points}")

    # 按目标ID组织跟踪数据
    target_tracks = {}

    for frame_idx, tracked_frame in enumerate(tracked_history):
        for track in tracked_frame:
            if len(track) >= 4:
                tid = int(track[3])
                if tid not in target_tracks:
                    target_tracks[tid] = []

                # 获取时间信息
                if frame_idx in frame_info:
                    time_stamp = frame_info[frame_idx]['time']
                else:
                    time_stamp = frame_idx * 0.05  # 默认时间步长

                target_tracks[tid].append((time_stamp, track[:3]))

    # 对每个目标的轨迹进行插值
    smoothed_tracks = {}
    for tid, track_history in target_tracks.items():
        if len(track_history) >= 2:
            print(f"处理目标 {tid}: {len(track_history)} 个跟踪点")
            smoothed_tracks[tid] = interpolate_trajectory(
                track_history, interpolation_points, method
            )
        else:
            smoothed_tracks[tid] = track_history

    # 重新组织为按时间排序的结果
    all_smoothed_points = []
    for tid, smoothed_track in smoothed_tracks.items():
        for time_stamp, position in smoothed_track:
            all_smoothed_points.append((time_stamp, position, tid))

    # 按时间排序
    all_smoothed_points.sort(key=lambda x: x[0])

    # 转换回原始格式
    smoothed_results = []
    current_time = None
    current_frame = []

    for time_stamp, position, tid in all_smoothed_points:
        if current_time is None:
            current_time = time_stamp

        # 如果时间差超过阈值，开始新的帧
        if abs(time_stamp - current_time) > 0.1:  # 0.1秒阈值
            if current_frame:
                smoothed_results.append(np.array(current_frame))
            current_frame = []
            current_time = time_stamp

        # 添加到当前帧
        track_point = np.concatenate([position, [tid]])
        current_frame.append(track_point)

    # 添加最后一帧
    if current_frame:
        smoothed_results.append(np.array(current_frame))

    print(f"平滑处理完成，生成 {len(smoothed_results)} 帧平滑结果")
    return smoothed_results

# ---------- 主测试流程 ----------
def main_test(use_grouped_frames=False, csv_file='/home/<USER>/My_Project/MSHNet_TensorRT_Infer/python/radar_simulation_data.csv'):
    """主测试函数 - 使用CSV数据进行跟踪测试

    参数:
    - use_grouped_frames: 是否使用120帧分组模式
    - csv_file: CSV数据文件路径
    """
    print("开始雷达目标跟踪测试...")

    if use_grouped_frames:
        print("使用120帧分组模式...")
    else:
        print("使用逐帧处理模式...")

    # 从CSV文件读取雷达数据
    frames, frame_info = load_radar_data_from_csv(
        csv_file,
        group_frames=use_grouped_frames,
        frames_per_group=120
    )

    if not frames:
        print("无法加载数据，测试终止")
        return

    # 初始化优化后的跟踪器（采用tracker.py策略+匀加速模型）
    tracker = RadarTracker(scan_period=6, beam_width=3)
    tracked_history = []

    print(f"\n开始处理 {len(frames)} {'组' if use_grouped_frames else '帧'}数据...")
    print("使用优化后的跟踪器：tracker.py策略 + 匀加速卡尔曼滤波")

    # 处理每帧数据
    for frame_idx, frame in enumerate(frames):
        # 获取当前帧信息
        current_frame_info = frame_info.get(frame_idx, {})

        # 聚类检测点（针对真实数据调整参数）
        clustered = cluster_detections(frame, eps=0.001, min_samples=1)

        # 更新跟踪器（使用简化的接口）
        tracked = tracker.update(clustered)
        tracked_history.append(tracked)

        # 打印状态
        if use_grouped_frames:
            frame_range = current_frame_info.get('frame_range', (0, 0))
            frames_in_group = current_frame_info.get('frames_in_group', 0)
            print(f"Group {frame_idx}: Frames {frame_range[0]}-{frame_range[1]} ({frames_in_group} frames), "
                  f"Detections {len(clustered)}, Tracks {len(tracked)}, "
                  f"True targets: {current_frame_info.get('num_targets', 0)}")
        else:
            if frame_idx % 10 == 0 or frame_idx == len(frames) - 1:
                beam_angle = current_frame_info.get('beam_angle', (frame_idx * 3) % 360)
                print(f"Frame {frame_idx}: Beam {beam_angle:.1f}°, "
                      f"Detections {len(clustered)}, Tracks {len(tracked)}, "
                      f"True targets: {current_frame_info.get('num_targets', 0)}")

    print("\n数据处理完成，开始性能评估...")

    # 计算跟踪性能指标
    metrics = calculate_tracking_metrics(frames, tracked_history)
    print_tracking_metrics(metrics)

    # 可视化跟踪效果对比
    print("\n生成可视化图表...")
    visualize(frames, tracked_history, save=True)

    return frames, tracked_history, metrics

def test_grouped_tracking_with_interpolation():
    """测试120帧分组跟踪并进行轨迹插值平滑"""
    print("="*60)
    print("测试120帧分组跟踪 + 轨迹插值平滑")
    print("="*60)

    # 使用分组模式进行跟踪
    frames, tracked_history, metrics = main_test(use_grouped_frames=True)

    if not frames or not tracked_history:
        print("跟踪失败，无法进行插值测试")
        return

    print("\n" + "="*40)
    print("开始轨迹插值和平滑处理...")
    print("="*40)

    # 获取帧信息
    _, frame_info = load_radar_data_from_csv(
        '/home/<USER>/My_Project/MSHNet_TensorRT_Infer/python/radar_simulation_data.csv',
        group_frames=True,
        frames_per_group=120
    )

    # 测试不同的插值方法
    interpolation_methods = ['linear', 'cubic', 'kalman']
    interpolation_points = 4  # 在每两个跟踪点之间插入8个点

    for method in interpolation_methods:
        print(f"\n--- 测试 {method} 插值方法 ---")
        try:
            smoothed_results = smooth_tracking_results(
                tracked_history, frame_info,
                interpolation_points=interpolation_points,
                method=method
            )

            print(f"{method} 插值完成:")
            print(f"  原始跟踪帧数: {len(tracked_history)}")
            print(f"  平滑后帧数: {len(smoothed_results)}")

            # 计算平滑后的性能指标
            if len(smoothed_results) > 0:
                # 为了评估，我们需要将原始帧数据扩展以匹配平滑结果
                print(f"  平滑效果: 从 {len(tracked_history)} 帧扩展到 {len(smoothed_results)} 帧")

                # 可选：可视化第一种方法的结果
                if method == interpolation_methods[0]:
                    print(f"  生成 {method} 插值的可视化...")
                    # 这里可以调用可视化函数

        except Exception as e:
            print(f"{method} 插值失败: {e}")

    print("\n" + "="*40)
    print("插值测试完成")
    print("="*40)

    return frames, tracked_history, frame_info

def compare_tracking_modes():
    """比较逐帧跟踪和分组跟踪的效果"""
    print("="*60)
    print("比较逐帧跟踪 vs 120帧分组跟踪")
    print("="*60)

    # 逐帧跟踪
    print("\n1. 逐帧跟踪模式:")
    print("-" * 30)
    frames1, tracked1, metrics1 = main_test(use_grouped_frames=False)

    print("\n2. 120帧分组跟踪模式:")
    print("-" * 30)
    frames2, tracked2, metrics2 = main_test(use_grouped_frames=True)

    # 比较结果
    print("\n" + "="*40)
    print("跟踪模式对比结果:")
    print("="*40)

    if metrics1 and metrics2:
        print(f"逐帧模式 - MOTA: {metrics1['mota']:.3f}, 精确度: {metrics1['precision']:.3f}")
        print(f"分组模式 - MOTA: {metrics2['mota']:.3f}, 精确度: {metrics2['precision']:.3f}")
        print(f"逐帧模式 - 跟踪帧数: {len(tracked1)}")
        print(f"分组模式 - 跟踪组数: {len(tracked2)}")

    return (frames1, tracked1, metrics1), (frames2, tracked2, metrics2)

if __name__ == '__main__':
    # 可以选择运行不同的测试
    import sys

    if len(sys.argv) > 1:
        mode = sys.argv[1]
        if mode == 'grouped':
            test_grouped_tracking_with_interpolation()
        elif mode == 'compare':
            compare_tracking_modes()
        elif mode == 'normal':
            main_test(use_grouped_frames=False)
        else:
            print("可用模式: normal, grouped, compare")
    else:
        # 默认运行分组跟踪测试
        test_grouped_tracking_with_interpolation()